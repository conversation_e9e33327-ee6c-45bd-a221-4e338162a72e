"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { toast } from "sonner";
import {
  ChevronRight,
  ChevronDown,
  Folder,
  FolderOpen,
  FileText,
  Plus,
  ChevronLeft,
  Search,
  Clock,
  Globe2,
  Users,
  MoreHorizontal,
  Trash2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { getNotes, createFolder, updateNoteParent, deleteNote } from "@/actions/actions";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import type { Note } from "@prisma/client";

// Interfaces
interface CollaborationMetadata {
  collaborationId?: string;
  collaborationStartedAt?: string;
  mergedFromPublicId?: string;
  mergedAt?: string;
}

interface NoteItem extends Note {
  children?: NoteItem[];
}

interface TreeItemProps {
  note: NoteItem;
  level: number;
  expandedFolders: Set<string>;
  onToggle: (noteId: string) => void;
  onSelect: (noteId: string) => void;
  onCreateNote: (parentId: string) => void;
  onCreateFolder: (parentId: string) => void;
  onDelete: (note: NoteItem) => void;
  onDrop: (draggedId: string, targetId: string) => void;
  activeNoteId: string | null;
  isCollapsed: boolean;
}

// Tree Item Component
const TreeItemComponent: React.FC<TreeItemProps> = ({
  note,
  level,
  expandedFolders,
  onToggle,
  onSelect,
  onCreateNote,
  onCreateFolder,
  onDelete,
  onDrop,
  activeNoteId,
  isCollapsed
}) => {
  const [isDragOver, setIsDragOver] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const hasChildren = note.children && note.children.length > 0;
  const isActive = activeNoteId === note.id;
  const isExpanded = expandedFolders.has(note.id);

  // Get appropriate icon
  const getIcon = () => {
    if (note.isFolder) {
      return isExpanded 
        ? <FolderOpen className="h-4 w-4 text-blue-500" />
        : <Folder className="h-4 w-4 text-muted-foreground" />;
    }

    // Parse metadata for collaborative info
    let isCollaborative = false;
    try {
      if (note.metadata) {
        const metadata: CollaborationMetadata = typeof note.metadata === 'string'
          ? JSON.parse(note.metadata)
          : note.metadata as CollaborationMetadata;
        isCollaborative = !!metadata.collaborationId;
      }
    } catch (e) {
      console.error('Error parsing metadata:', e);
    }

    if (isCollaborative) {
      return <Users className="text-blue-400 h-4 w-4" />;
    } else if (note.isPublished) {
      return <Globe2 className="text-green-400 h-4 w-4" />;
    } else {
      return <FileText className="h-4 w-4 text-muted-foreground" />;
    }
  };

  // Format date
  const formatDate = (date: string | Date): string => {
    if (!date) return "";
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, 'MMM d');
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent) => {
    setIsDragging(true);
    e.dataTransfer.setData('text/plain', note.id);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent) => {
    if (note.isFolder) {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'move';
      setIsDragOver(true);
    }
  };

  const handleDragLeave = () => {
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (note.isFolder) {
      const draggedId = e.dataTransfer.getData('text/plain');
      if (draggedId !== note.id) {
        onDrop(draggedId, note.id);
      }
    }
  };

  const handleClick = () => {
    if (note.isFolder) {
      onToggle(note.id);
    } else {
      onSelect(note.id);
    }
  };

  return (
    <>
      <div
        className={cn(
          "flex items-center px-2 py-1 mx-1 my-0.5 rounded cursor-pointer select-none transition-colors text-sm min-h-6 relative group",
          "hover:bg-accent/10",
          isActive && "bg-accent/15 text-accent-foreground",
          note.isFolder && "font-medium",
          isDragOver && "bg-primary/10 border border-dashed border-primary",
          isDragging && "opacity-50"
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={handleClick}
        draggable
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        {/* Chevron */}
        <div className="w-4 h-4 flex items-center justify-center mr-1 flex-shrink-0">
          {note.isFolder && hasChildren && (
            isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )
          )}
        </div>

        {/* Icon */}
        <div className="w-4 h-4 mr-2 flex-shrink-0">
          {getIcon()}
        </div>

        {/* Title */}
        <span className="flex-1 truncate text-xs">
          {note.title || "Untitled"}
        </span>

        {/* Date for files */}
        {!isCollapsed && !note.isFolder && note.updatedAt && (
          <span className="text-xs text-muted-foreground ml-auto opacity-60">
            {formatDate(note.updatedAt)}
          </span>
        )}

        {/* Actions */}
        {!isCollapsed && (
          <div className="opacity-0 group-hover:opacity-100 transition-opacity ml-1">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 text-muted-foreground hover:bg-accent/20"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreHorizontal className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" side="right" className="w-48">
                {note.isFolder && (
                  <>
                    <DropdownMenuItem onClick={() => onCreateNote(note.id)}>
                      <FileText className="mr-2 h-3.5 w-3.5" /> New Note
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onCreateFolder(note.id)}>
                      <Folder className="mr-2 h-3.5 w-3.5" /> New Folder
                    </DropdownMenuItem>
                  </>
                )}
                <DropdownMenuItem
                  onClick={() => onDelete(note)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="mr-2 h-3.5 w-3.5" /> Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        )}
      </div>

      {/* Render children */}
      {note.isFolder && isExpanded && hasChildren && (
        <>
          {note.children!.map((child) => (
            <TreeItemComponent
              key={child.id}
              note={child}
              level={level + 1}
              expandedFolders={expandedFolders}
              onToggle={onToggle}
              onSelect={onSelect}
              onCreateNote={onCreateNote}
              onCreateFolder={onCreateFolder}
              onDelete={onDelete}
              onDrop={onDrop}
              activeNoteId={activeNoteId}
              isCollapsed={isCollapsed}
            />
          ))}
        </>
      )}
    </>
  );
};

// Main component
export default function NoteClientSidebar() {
  const router = useRouter();
  const pathname = usePathname();
  
  const [notes, setNotes] = useState<Note[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['root']));
  const [folderName, setFolderName] = useState<string>("");
  const [parentFolderId, setParentFolderId] = useState<string | undefined>(undefined);
  const [isDialogOpen, setIsDialogOpen] = useState<boolean>(false);
  const [itemToDelete, setItemToDelete] = useState<NoteItem | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);

  // Get active note ID from pathname
  const activeNoteId = pathname.includes('/note/') ? pathname.split('/').pop() || null : null;

  // Fetch notes
  const fetchNotes = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const response = await getNotes();
      if (response.success && response.notes) {
        setNotes(response.notes as Note[]);
      } else {
        setNotes([]);
      }
    } catch (error) {
      console.error("Failed to fetch notes", error);
      setNotes([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchNotes();
  }, []);

  // Transform flat notes to tree structure
  const buildTree = (notes: Note[]): NoteItem[] => {
    const noteMap = new Map<string, NoteItem>();
    const rootNotes: NoteItem[] = [];

    // Create map of all notes
    notes.forEach(note => {
      noteMap.set(note.id, { ...note, children: [] });
    });

    // Build tree structure
    notes.forEach(note => {
      const noteItem = noteMap.get(note.id)!;
      if (note.parentId && noteMap.has(note.parentId)) {
        const parent = noteMap.get(note.parentId)!;
        parent.children!.push(noteItem);
      } else {
        rootNotes.push(noteItem);
      }
    });

    return rootNotes;
  };

  const treeData = buildTree(notes);

  // Helper functions
  const toggleSidebar = (): void => {
    setIsCollapsed(!isCollapsed);
  };

  const handleToggleFolder = (noteId: string): void => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(noteId)) {
        newSet.delete(noteId);
      } else {
        newSet.add(noteId);
      }
      return newSet;
    });
  };

  const handleSelectNote = (noteId: string): void => {
    router.push(`/hr/workspace/note/${noteId}`);
  };

  const handleCreateNote = (parentId?: string): void => {
    router.push('/hr/workspace/note' + (parentId ? `?parentId=${parentId}` : ''));
  };

  const openFolderDialog = (parentId?: string): void => {
    setParentFolderId(parentId);
    setFolderName("");
    setIsDialogOpen(true);
  };

  const handleCreateFolder = async (): Promise<void> => {
    if (!folderName.trim()) {
      toast.error("Folder name cannot be empty");
      return;
    }

    try {
      const result = await createFolder(folderName, parentFolderId);
      if (result.success) {
        toast.success(`Folder "${folderName}" created`);
        fetchNotes();
        setIsDialogOpen(false);
      } else {
        toast.error(result.error || 'Failed to create folder');
      }
    } catch (error) {
      console.error('Error creating folder:', error);
      toast.error('An error occurred while creating folder');
    }
  };

  const handleDeleteItem = (item: NoteItem): void => {
    setItemToDelete(item);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async (): Promise<void> => {
    if (!itemToDelete) return;

    toast.promise(
      deleteNote(itemToDelete.id),
      {
        loading: `Deleting ${itemToDelete.isFolder ? 'folder' : 'note'}...`,
        success: (result) => {
          if (result.success) {
            fetchNotes();
            return `${itemToDelete.isFolder ? 'Folder' : 'Note'} deleted successfully`;
          } else {
            throw new Error(result.error);
          }
        },
        error: (err: Error) => `Failed to delete: ${err.message || 'Unknown error'}`
      }
    );

    setIsDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  const handleDrop = async (draggedId: string, targetId: string): Promise<void> => {
    try {
      const result = await updateNoteParent(draggedId, targetId);
      if (result.success) {
        toast.success('Item moved successfully');
        fetchNotes();
      } else {
        toast.error(result.error || 'Failed to move item');
      }
    } catch (error) {
      console.error('Error moving item:', error);
      toast.error('An error occurred while moving the item');
    }
  };

  // Filter notes based on search
  const filteredTreeData = searchQuery.trim()
    ? notes.filter(note =>
        note.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (typeof note.content === 'string' && note.content.toLowerCase().includes(searchQuery.toLowerCase()))
      ).map(note => ({ ...note, children: [] }))
    : treeData;

  // Get published and recent notes
  const publishedNotes = notes.filter(note => note.isPublished);
  const recentNotes = notes
    .filter(note => !note.isFolder && new Date(note.updatedAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
    .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
    .slice(0, 5);

  return (
    <div className={cn(
      "w-72 h-full bg-background border-r border-border flex flex-col transition-all duration-200",
      isCollapsed && "w-12"
    )}>
      {/* Header */}
      <div className="p-3 border-b border-border flex items-center justify-between min-h-12">
        {!isCollapsed && (
          <h3 className="font-semibold text-sm">Notes</h3>
        )}
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={toggleSidebar}
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
        </Button>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-2">
        {!isCollapsed && (
          <>
            {/* Search */}
            <div className="mb-3">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
                <Input
                  placeholder="Search notes..."
                  value={searchQuery}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
                  className="pl-7 h-7 text-xs"
                />
              </div>
            </div>

            {/* Add buttons */}
            <div className="flex gap-1 mb-4">
              <Button
                variant="outline"
                size="sm"
                className="flex-1 h-7 text-xs"
                onClick={() => handleCreateNote()}
              >
                <Plus className="h-3 w-3 mr-1" />
                Note
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex-1 h-7 text-xs"
                onClick={() => openFolderDialog()}
              >
                <Folder className="h-3 w-3 mr-1" />
                Folder
              </Button>
            </div>
          </>
        )}

        {/* Loading skeleton */}
        {isLoading && (
          <div className="space-y-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center gap-2 px-2 py-1">
                <Skeleton className="h-3 w-3 rounded-sm" />
                <Skeleton className="h-3 flex-1 rounded-sm" />
              </div>
            ))}
          </div>
        )}

        {/* Tree content */}
        {!isLoading && (
          <>
            {/* Workspace section */}
            {!isCollapsed && <div className="text-xs font-semibold text-muted-foreground mb-2 uppercase tracking-wide">Workspace</div>}
            <div className="group">
              {filteredTreeData.map((note) => (
                <TreeItemComponent
                  key={note.id}
                  note={note}
                  level={0}
                  expandedFolders={expandedFolders}
                  onToggle={handleToggleFolder}
                  onSelect={handleSelectNote}
                  onCreateNote={handleCreateNote}
                  onCreateFolder={openFolderDialog}
                  onDelete={handleDeleteItem}
                  onDrop={handleDrop}
                  activeNoteId={activeNoteId}
                  isCollapsed={isCollapsed}
                />
              ))}
            </div>

            {/* Published notes section */}
            {!isCollapsed && publishedNotes.length > 0 && (
              <>
                <div className="text-xs font-semibold text-muted-foreground mb-2 mt-6 uppercase tracking-wide">Published</div>
                <div className="space-y-0.5">
                  {publishedNotes.slice(0, 5).map((note) => (
                    <div
                      key={note.id}
                      onClick={() => handleSelectNote(note.id)}
                      className={cn(
                        "flex items-center gap-2 px-2 py-1 rounded cursor-pointer hover:bg-accent/10 text-xs",
                        activeNoteId === note.id && "bg-accent/15"
                      )}
                    >
                      <Globe2 className="h-3 w-3 text-green-500" />
                      <span className="truncate">{note.title || "Untitled"}</span>
                    </div>
                  ))}
                </div>
              </>
            )}

            {/* Recent notes section */}
            {!isCollapsed && recentNotes.length > 0 && (
              <>
                <div className="text-xs font-semibold text-muted-foreground mb-2 mt-6 uppercase tracking-wide">Recent</div>
                <div className="space-y-0.5">
                  {recentNotes.map((note) => (
                    <div
                      key={note.id}
                      onClick={() => handleSelectNote(note.id)}
                      className={cn(
                        "flex items-center gap-2 px-2 py-1 rounded cursor-pointer hover:bg-accent/10 text-xs",
                        activeNoteId === note.id && "bg-accent/15"
                      )}
                    >
                      <Clock className="h-3 w-3 text-muted-foreground" />
                      <span className="truncate">{note.title || "Untitled"}</span>
                    </div>
                  ))}
                </div>
              </>
            )}
          </>
        )}
      </div>

      {/* Dialogs */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="Folder name"
              value={folderName}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setFolderName(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleCreateFolder()}
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateFolder}>
              Create
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the {itemToDelete?.isFolder ? 'folder' : 'note'} "{itemToDelete?.title || 'Untitled'}"
              {itemToDelete?.isFolder ? ' and all its contents' : ''}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={confirmDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
